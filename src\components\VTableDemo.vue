<template>
  <div class="demo-container">
    <div class="control-panel">
      <h3>VTable Sheet 组件演示</h3>
      
      <!-- 数据切换按钮 -->
      <div class="button-group">
        <h4>数据切换:</h4>
        <button @click="switchToEmployeeData" class="btn btn-primary">员工数据</button>
        <button @click="switchToProductData" class="btn btn-primary">产品数据</button>
        <button @click="switchToSalesData" class="btn btn-primary">销售数据</button>
      </div>

      <!-- 操作按钮 -->
      <div class="button-group">
        <h4>数据操作:</h4>
        <button @click="addNewRow" class="btn btn-success">添加行</button>
        <button @click="deleteLastRow" class="btn btn-danger">删除最后一行</button>
        <button @click="updateFirstCell" class="btn btn-warning">更新第一个单元格</button>
        <button @click="exportData" class="btn btn-info">导出数据</button>
      </div>

      <!-- 当前数据信息 -->
      <div class="info-panel">
        <h4>当前数据信息:</h4>
        <p>数据类型: {{ currentDataType }}</p>
        <p>行数: {{ currentRowCount }}</p>
        <p>列数: {{ currentColumns.length }}</p>
      </div>
    </div>

    <!-- VTable Sheet 组件 -->
    <div class="table-wrapper">
      <VTableSheetComponent
        ref="tableRef"
        :data="currentData"
        :columns="currentColumns"
        :sheet-title="currentDataType"
        :show-formula-bar="true"
        :show-sheet-tab="true"
        @sheet-ready="onSheetReady"
        @data-changed="onDataChanged"
        @cell-click="onCellClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import VTableSheetComponent from './VTableSheetComponent.vue'
import type { VTableSheet } from '@visactor/vtable-sheet'

// 组件引用
const tableRef = ref<InstanceType<typeof VTableSheetComponent> | null>(null)

// 当前数据状态
const currentDataType = ref('员工数据')
const currentRowCount = ref(0)

// 不同类型的数据
const employeeData = ref([
  ['张三', 28, '技术部', '高级工程师'],
  ['李四', 32, '市场部', '市场经理'],
  ['王五', 25, '人事部', '人事专员'],
  ['赵六', 30, '财务部', '会计师']
])

const productData = ref([
  ['iPhone 15', 'Apple', 7999, 100],
  ['MacBook Pro', 'Apple', 15999, 50],
  ['iPad Air', 'Apple', 4599, 80],
  ['AirPods Pro', 'Apple', 1899, 200]
])

const salesData = ref([
  ['2024-01', 150000, 120000, 30000],
  ['2024-02', 180000, 140000, 40000],
  ['2024-03', 200000, 160000, 40000],
  ['2024-04', 220000, 180000, 40000]
])

// 不同类型的列配置
const employeeColumns = [
  { title: '姓名', width: 100 },
  { title: '年龄', width: 80 },
  { title: '部门', width: 120 },
  { title: '职位', width: 150 }
]

const productColumns = [
  { title: '产品名称', width: 150 },
  { title: '品牌', width: 100 },
  { title: '价格', width: 100 },
  { title: '库存', width: 80 }
]

const salesColumns = [
  { title: '月份', width: 100 },
  { title: '收入', width: 120 },
  { title: '成本', width: 120 },
  { title: '利润', width: 120 }
]

// 当前数据和列配置
const currentData = ref(employeeData.value)
const currentColumns = ref(employeeColumns)

// 计算当前行数
const updateRowCount = () => {
  currentRowCount.value = currentData.value.length
}

// 数据切换方法
const switchToEmployeeData = () => {
  currentDataType.value = '员工数据'
  currentData.value = [...employeeData.value]
  currentColumns.value = employeeColumns
  updateRowCount()
}

const switchToProductData = () => {
  currentDataType.value = '产品数据'
  currentData.value = [...productData.value]
  currentColumns.value = productColumns
  updateRowCount()
}

const switchToSalesData = () => {
  currentDataType.value = '销售数据'
  currentData.value = [...salesData.value]
  currentColumns.value = salesColumns
  updateRowCount()
}

// 操作方法
const addNewRow = () => {
  if (!tableRef.value) return

  let newRow: any[] = []
  
  switch (currentDataType.value) {
    case '员工数据':
      newRow = [`新员工${currentData.value.length + 1}`, 25, '新部门', '新职位']
      break
    case '产品数据':
      newRow = [`新产品${currentData.value.length + 1}`, '新品牌', 999, 10]
      break
    case '销售数据':
      newRow = [`2024-${String(currentData.value.length + 5).padStart(2, '0')}`, 100000, 80000, 20000]
      break
  }
  
  tableRef.value.addRow(newRow)
  updateRowCount()
}

const deleteLastRow = () => {
  if (!tableRef.value) return
  
  const data = tableRef.value.getData()
  if (data.length > 1) {
    tableRef.value.deleteRow(data.length - 1)
    updateRowCount()
  } else {
    alert('至少需要保留一行数据')
  }
}

const updateFirstCell = () => {
  if (!tableRef.value) return
  
  const timestamp = new Date().toLocaleTimeString()
  tableRef.value.updateCell(0, 0, `更新于${timestamp}`)
}

const exportData = () => {
  if (!tableRef.value) return
  
  const data = tableRef.value.getData()
  console.log('导出数据:', data)
  
  // 创建CSV格式的数据
  const csvContent = [
    currentColumns.value.map(col => col.title).join(','),
    ...data.map(row => row.join(','))
  ].join('\n')
  
  // 下载文件
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${currentDataType.value}_${new Date().toISOString().split('T')[0]}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 事件处理
const onSheetReady = (instance: VTableSheet) => {
  console.log('表格初始化完成:', instance)
  updateRowCount()
}

const onDataChanged = (data: any[][]) => {
  console.log('数据已变化:', data)
  currentData.value = data
  updateRowCount()
}

const onCellClick = (event: any) => {
  console.log('单元格点击事件:', event)
}

// 初始化
updateRowCount()
</script>

<style scoped>
.demo-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.control-panel {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.control-panel h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.button-group {
  margin-bottom: 20px;
}

.button-group h4 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.btn {
  padding: 8px 16px;
  margin-right: 10px;
  margin-bottom: 5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #1890ff;
  color: white;
}

.btn-primary:hover {
  background-color: #40a9ff;
}

.btn-success {
  background-color: #52c41a;
  color: white;
}

.btn-success:hover {
  background-color: #73d13d;
}

.btn-danger {
  background-color: #ff4d4f;
  color: white;
}

.btn-danger:hover {
  background-color: #ff7875;
}

.btn-warning {
  background-color: #faad14;
  color: white;
}

.btn-warning:hover {
  background-color: #ffc53d;
}

.btn-info {
  background-color: #13c2c2;
  color: white;
}

.btn-info:hover {
  background-color: #36cfc9;
}

.info-panel {
  background-color: #f0f0f0;
  padding: 15px;
  border-radius: 4px;
}

.info-panel h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.info-panel p {
  margin: 5px 0;
  color: #666;
  font-size: 13px;
}

.table-wrapper {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  overflow: hidden;
}
</style>
