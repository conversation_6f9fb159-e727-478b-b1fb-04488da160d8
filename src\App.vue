<template>
  <div class="app-container">
    <div class="tab-bar">
      <button
        @click="currentTab = 'original'"
        :class="['tab-btn', { active: currentTab === 'original' }]"
      >
        原始实现
      </button>
      <button
        @click="currentTab = 'component'"
        :class="['tab-btn', { active: currentTab === 'component' }]"
      >
        组件化实现
      </button>
    </div>

    <!-- 原始实现 -->
    <div v-if="currentTab === 'original'" class="tab-content">
      <div class="button-bar">
        <button @click="testQueryData" class="test-btn">查询数据</button>
        <button @click="testModifyData" class="test-btn">修改数据</button>
        <button @click="testAddRow" class="test-btn">添加行</button>
        <button @click="testDeleteRow" class="test-btn">删除行</button>
        <button @click="testUpdateCell" class="test-btn">更新单元格</button>
        <button @click="testReset" class="test-btn">重置</button>
        <button @click="testSetData" class="test-btn">测试setData</button>
        <button @click="debugTable" class="test-btn">调试表格</button>
      </div>
      <div class="table-container">
        <div id="vtable-sheet-container" class="vtable-sheet-container"></div>
      </div>
    </div>

    <!-- 组件化实现 -->
    <div v-if="currentTab === 'component'" class="tab-content">
      <VTableDemo />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, nextTick } from 'vue'
import { VTableSheet } from '@visactor/vtable-sheet'
import VTableDemo from './components/VTableDemo.vue'

const sheetInstance = ref<VTableSheet | null>(null)
const currentTab = ref<'original' | 'component'>('original')

// 初始数据
const initialData = [
  ['张三', 28, '技术部'],
  ['李四', 32, '市场部'],
  ['王五', 25, '人事部']
]

//初始化100万行进行性能测试
const initialDataLarge = []
for (let i = 0; i < 1000000; i++) {
  initialDataLarge.push(['姓名' + i, i, '部门' + i])
}

// 初始化原始表格
const initOriginalTable = async () => {
  if (currentTab.value !== 'original') return

  await nextTick()
  const container = document.getElementById('vtable-sheet-container')
  if (!container) {
    console.error('Container not found: vtable-sheet-container')
    return
  }

  console.log('开始初始化VTableSheet...')
  console.log('Container:', container)
  console.log('Container dimensions:', container.offsetWidth, 'x', container.offsetHeight)
  console.log('Initial data:', initialData)

  try {
    sheetInstance.value = new VTableSheet(container, {
      showFormulaBar: true,  // 显示公式栏
      showSheetTab: true,    // 显示底部表格页切换栏
      defaultRowHeight: 25,  // 默认行高
      defaultColWidth: 100,  // 默认列宽
      sheets: [
        {
          sheetKey: 'sheet1',   // 表格页唯一标识
          sheetTitle: '表格1',  // 表格页显示名称
          columns: [            // 列定义
            { title: '姓名', width: 100 },
            { title: '年龄', width: 80 },
            { title: '部门', width: 120 }
          ],
          data: initialData,    // 表格数据
          active: true,          // 设置为当前激活的表格页
          filter: true
        }
      ]
    })

    console.log('VTableSheet初始化完成:', sheetInstance.value)

    // 检查activeSheet
    const activeSheet = sheetInstance.value.getActiveSheet()
    console.log('Active sheet:', activeSheet)
    if (activeSheet) {
      console.log('Active sheet data:', activeSheet.getData())
      console.log('Active sheet tableInstance:', activeSheet.tableInstance)
    }
  } catch (error) {
    console.error('VTableSheet初始化失败:', error)
  }
}

// 监听标签切换
watch(currentTab, async (newTab) => {
  if (newTab === 'original') {
    await nextTick()
    initOriginalTable()
  } else {
    // 切换到组件模式时销毁原始表格
    if (sheetInstance.value) {
      try {
        // VTableSheet 可能没有 destroy 方法，我们直接设置为 null
        sheetInstance.value = null
      } catch (error) {
        console.warn('Failed to destroy sheet instance:', error)
        sheetInstance.value = null
      }
    }
  }
})

onMounted(() => {
  if (currentTab.value === 'original') {
    initOriginalTable()
  }
})

// 查询数据
const testQueryData = () => {
  if (sheetInstance.value) {
    const activeSheet = sheetInstance.value.getActiveSheet()
    if (activeSheet) {
      const data = activeSheet.options.data
      console.log('当前数据:', data)
      // 弹出显示详细数据内容
      if (data && Array.isArray(data)) {
        let dataInfo = `查询到 ${data.length} 行数据:\n\n`
        data.forEach((row: any[], index: number) => {
          dataInfo += `第${index + 1}行: ${JSON.stringify(row)}\n`
        })
        alert(dataInfo)
      }
    }
  }
}

// 修改数据
const testModifyData = () => {
  if (sheetInstance.value) {
    const activeSheet = sheetInstance.value.getActiveSheet()
    if (activeSheet && activeSheet.tableInstance) {
      const newData = [
        ['赵六', 30, '财务部'],
        ['钱七', 35, '运营部'],
        ['孙八', 80, '产品部']
      ]
      activeSheet.tableInstance.setRecords(newData)//不会修改options.data里面的数据，需要手动更正
      for (let i = 1; i < 100; i++) {
        activeSheet.setCellValue(i, 2, '修改后的值')
        activeSheet.tableInstance.changeCellValue(2, i, '修改后的值');
      }
      console.log('数据已修改')
      alert('数据已修改')
    }
  }
}

// 添加行
const testAddRow = () => {
  if (sheetInstance.value) {
    const activeSheet = sheetInstance.value.getActiveSheet()
    if (activeSheet && activeSheet.tableInstance) {
      const currentData = activeSheet.getData()
      const newRow = ['新用户', 25, '新部门']
      currentData.push(newRow)
      activeSheet.tableInstance.setRecords(currentData)
      console.log('已添加新行')
      alert('已添加新行')
    }
  }
}

// 删除行
const testDeleteRow = () => {
  if (sheetInstance.value) {
    const activeSheet = sheetInstance.value.getActiveSheet()
    if (activeSheet && activeSheet.tableInstance) {
      const currentData = activeSheet.getData()
      if (currentData.length > 1) {
        currentData.pop()
        activeSheet.tableInstance.setRecords(currentData)
        console.log('已删除最后一行')
        alert('已删除最后一行')
      } else {
        alert('至少需要保留一行数据')
      }
    }
  }
}

// 更新单元格
const testUpdateCell = () => {
  if (sheetInstance.value) {
    const activeSheet = sheetInstance.value.getActiveSheet()
    if (activeSheet) {
      // 更新第一行第一列的单元格
      activeSheet.setCellValue(0, 0, '已更新')
      console.log('已更新单元格 (0,0)')
      alert('已更新单元格 (0,0)')
    }
  }
}

// 重置
const testReset = () => {
  if (sheetInstance.value) {
    const activeSheet = sheetInstance.value.getActiveSheet()
    if (activeSheet && activeSheet.tableInstance) {
      activeSheet.tableInstance.setRecords(initialData)
      console.log('已重置数据')
      alert('已重置数据')
    }
  }
}

// 测试setData函数
const testSetData = () => {
  if (sheetInstance.value) {
    const activeSheet = sheetInstance.value.getActiveSheet()
    if (activeSheet) {
      try {
        const testData = [
          ['测试用户1', 25, '测试部门1'],
          ['测试用户2', 30, '测试部门2'],
          ['测试用户3', 35, '测试部门3']
        ]

        console.log('开始测试setData函数...')
        console.log('当前activeSheet:', activeSheet)
        console.log('当前tableInstance:', activeSheet.tableInstance)

        // 方法1: 使用 WorkSheet 的 setData 方法
        console.log('使用 WorkSheet.setData 方法...')
        activeSheet.setData(testData)
        console.log('WorkSheet.setData 执行完成')

        // 验证数据是否设置成功
        const currentData = activeSheet.getData()
        console.log('设置后的数据:', currentData)

        alert('setData测试完成，请查看控制台输出')

      } catch (error) {
        console.error('setData测试出错:', error)
        alert('setData测试出错: ' + (error instanceof Error ? error.message : String(error)))
      }
    } else {
      console.error('activeSheet不存在')
      alert('错误：activeSheet不存在')
    }
  } else {
    console.error('sheetInstance不存在')
    alert('错误：sheetInstance不存在')
  }
}

// 调试表格
const debugTable = () => {
  console.log('=== 表格调试信息 ===')
  console.log('sheetInstance:', sheetInstance.value)

  if (sheetInstance.value) {
    console.log('getActiveSheet():', sheetInstance.value.getActiveSheet())
    const activeSheet = sheetInstance.value.getActiveSheet()

    if (activeSheet) {
      console.log('activeSheet.getData():', activeSheet.getData())
      console.log('activeSheet.tableInstance:', activeSheet.tableInstance)
      console.log('activeSheet.getElement():', activeSheet.getElement())
      console.log('activeSheet.getContainer():', activeSheet.getContainer())

      // 检查容器元素
      const container = document.getElementById('vtable-sheet-container')
      console.log('Container element:', container)
      if (container) {
        console.log('Container dimensions:', {
          width: container.offsetWidth,
          height: container.offsetHeight,
          clientWidth: container.clientWidth,
          clientHeight: container.clientHeight
        })
        console.log('Container children:', container.children)
        console.log('Container innerHTML length:', container.innerHTML.length)
      }
    }
  }

  alert('调试信息已输出到控制台')
}
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

.tab-bar {
  display: flex;
  gap: 0;
  margin-bottom: 20px;
}

.tab-btn {
  padding: 12px 24px;
  background-color: #ffffff;
  color: #666;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  border-radius: 0;
}

.tab-btn:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.tab-btn:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  border-left: none;
}

.tab-btn.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.tab-btn:hover:not(.active) {
  background-color: #f0f0f0;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.button-bar {
  display: flex;
  gap: 10px;
  padding: 15px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.test-btn:hover {
  background-color: #40a9ff;
}

.table-container {
  flex: 1;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  overflow: hidden;
}

.vtable-sheet-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
  border: 2px solid #ccc;
  background-color: white;
}
</style>
