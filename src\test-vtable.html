<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VTable Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
        }
        .info {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
        }
        .buttons {
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            margin-right: 10px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="info">
        <h2>VTable Sheet 测试页面</h2>
        <p>这是一个独立的测试页面，用于验证 VTableSheet 是否能正常工作。</p>
        <p>如果你能看到下面的表格，说明 VTableSheet 库正常工作。</p>
    </div>
    
    <div class="buttons">
        <button class="btn" onclick="testSetData()">测试 setData</button>
        <button class="btn" onclick="testGetData()">获取数据</button>
        <button class="btn" onclick="testAddRow()">添加行</button>
        <button class="btn" onclick="resetData()">重置数据</button>
    </div>
    
    <div id="vtable-container" class="container"></div>

    <script type="module">
        import { VTableSheet } from '@visactor/vtable-sheet';
        
        let sheetInstance = null;
        
        // 初始数据
        const initialData = [
            ['张三', 28, '技术部'],
            ['李四', 32, '市场部'],
            ['王五', 25, '人事部']
        ];
        
        // 初始化表格
        function initTable() {
            const container = document.getElementById('vtable-container');
            
            console.log('开始初始化 VTableSheet...');
            console.log('Container:', container);
            
            try {
                sheetInstance = new VTableSheet(container, {
                    showFormulaBar: true,
                    showSheetTab: true,
                    defaultRowHeight: 25,
                    defaultColWidth: 100,
                    sheets: [
                        {
                            sheetKey: 'sheet1',
                            sheetTitle: '测试表格',
                            columns: [
                                { title: '姓名', width: 100 },
                                { title: '年龄', width: 80 },
                                { title: '部门', width: 120 }
                            ],
                            data: initialData,
                            active: true,
                            filter: true
                        }
                    ]
                });
                
                console.log('VTableSheet 初始化成功:', sheetInstance);
                
                // 将函数暴露到全局作用域
                window.testSetData = testSetData;
                window.testGetData = testGetData;
                window.testAddRow = testAddRow;
                window.resetData = resetData;
                
            } catch (error) {
                console.error('VTableSheet 初始化失败:', error);
                container.innerHTML = '<div style="padding: 20px; color: red;">VTableSheet 初始化失败: ' + error.message + '</div>';
            }
        }
        
        // 测试 setData
        function testSetData() {
            if (!sheetInstance) {
                alert('表格未初始化');
                return;
            }
            
            try {
                const activeSheet = sheetInstance.getActiveSheet();
                if (activeSheet && activeSheet.tableInstance) {
                    const testData = [
                        ['测试用户1', 25, '测试部门1'],
                        ['测试用户2', 30, '测试部门2'],
                        ['测试用户3', 35, '测试部门3']
                    ];
                    
                    console.log('开始测试 setData...');
                    activeSheet.tableInstance.setRecords(testData);
                    console.log('setData 执行完成');
                    
                    alert('setData 测试完成');
                } else {
                    alert('无法获取 activeSheet 或 tableInstance');
                }
            } catch (error) {
                console.error('setData 测试失败:', error);
                alert('setData 测试失败: ' + error.message);
            }
        }
        
        // 获取数据
        function testGetData() {
            if (!sheetInstance) {
                alert('表格未初始化');
                return;
            }
            
            try {
                const activeSheet = sheetInstance.getActiveSheet();
                if (activeSheet) {
                    const data = activeSheet.getData();
                    console.log('当前数据:', data);
                    alert('数据已输出到控制台，共 ' + data.length + ' 行');
                } else {
                    alert('无法获取 activeSheet');
                }
            } catch (error) {
                console.error('获取数据失败:', error);
                alert('获取数据失败: ' + error.message);
            }
        }
        
        // 添加行
        function testAddRow() {
            if (!sheetInstance) {
                alert('表格未初始化');
                return;
            }
            
            try {
                const activeSheet = sheetInstance.getActiveSheet();
                if (activeSheet && activeSheet.tableInstance) {
                    const currentData = activeSheet.getData();
                    const newRow = ['新用户', 26, '新部门'];
                    currentData.push(newRow);
                    activeSheet.tableInstance.setRecords(currentData);
                    
                    console.log('添加行完成');
                    alert('添加行完成');
                } else {
                    alert('无法获取 activeSheet 或 tableInstance');
                }
            } catch (error) {
                console.error('添加行失败:', error);
                alert('添加行失败: ' + error.message);
            }
        }
        
        // 重置数据
        function resetData() {
            if (!sheetInstance) {
                alert('表格未初始化');
                return;
            }
            
            try {
                const activeSheet = sheetInstance.getActiveSheet();
                if (activeSheet && activeSheet.tableInstance) {
                    activeSheet.tableInstance.setRecords(initialData);
                    console.log('重置数据完成');
                    alert('重置数据完成');
                } else {
                    alert('无法获取 activeSheet 或 tableInstance');
                }
            } catch (error) {
                console.error('重置数据失败:', error);
                alert('重置数据失败: ' + error.message);
            }
        }
        
        // 页面加载完成后初始化表格
        document.addEventListener('DOMContentLoaded', initTable);
    </script>
</body>
</html>
