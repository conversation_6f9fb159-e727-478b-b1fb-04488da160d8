<template>
  <div class="vtable-sheet-wrapper">
    <div :id="containerId" class="vtable-sheet-container"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch, nextTick } from 'vue'
import { VTableSheet } from '@visactor/vtable-sheet'

// 定义组件的 props
interface Props {
  data?: any[][]
  columns?: Array<{ title: string; width?: number }>
  showFormulaBar?: boolean
  showSheetTab?: boolean
  defaultRowHeight?: number
  defaultColWidth?: number
  sheetTitle?: string
  containerId?: string
}

// 定义组件的 emits
interface Emits {
  (e: 'sheet-ready', instance: VTableSheet): void
  (e: 'data-changed', data: any[][]): void
  (e: 'cell-click', event: any): void
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  columns: () => [
    { title: '列1', width: 100 },
    { title: '列2', width: 100 },
    { title: '列3', width: 100 }
  ],
  showFormulaBar: true,
  showSheetTab: true,
  defaultRowHeight: 25,
  defaultColWidth: 100,
  sheetTitle: '表格1',
  containerId: () => `vtable-container-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
})

const emit = defineEmits<Emits>()

const sheetInstance = ref<VTableSheet | null>(null)

// 初始化表格
const initSheet = async () => {
  await nextTick()
  
  const container = document.getElementById(props.containerId)
  if (!container) {
    console.error('Container not found:', props.containerId)
    return
  }

  try {
    sheetInstance.value = new VTableSheet(container, {
      showFormulaBar: props.showFormulaBar,
      showSheetTab: props.showSheetTab,
      defaultRowHeight: props.defaultRowHeight,
      defaultColWidth: props.defaultColWidth,
      sheets: [
        {
          sheetKey: 'sheet1',
          sheetTitle: props.sheetTitle,
          columns: props.columns,
          data: props.data,
          active: true,
          filter: true
        }
      ]
    })

    // 监听事件
    if (sheetInstance.value) {
      // 监听单元格点击事件
      sheetInstance.value.on('click_cell', (event: any) => {
        emit('cell-click', event)
      })

      // 监听数据变化事件
      sheetInstance.value.on('change_cell_value', () => {
        const activeSheet = sheetInstance.value?.getActiveSheet()
        if (activeSheet) {
          const currentData = activeSheet.getData()
          emit('data-changed', currentData)
        }
      })

      emit('sheet-ready', sheetInstance.value)
    }
  } catch (error) {
    console.error('Failed to initialize VTableSheet:', error)
  }
}

// 监听数据变化，更新表格
watch(() => props.data, (newData) => {
  if (sheetInstance.value && newData) {
    const activeSheet = sheetInstance.value.getActiveSheet()
    if (activeSheet && activeSheet.tableInstance) {
      activeSheet.tableInstance.setRecords(newData)
    }
  }
}, { deep: true })

// 监听列配置变化
watch(() => props.columns, () => {
  // 如果列配置变化，需要重新初始化表格
  if (sheetInstance.value) {
    sheetInstance.value.destroy()
    sheetInstance.value = null
    initSheet()
  }
}, { deep: true })

// 暴露方法给父组件
const getSheetInstance = () => sheetInstance.value
const getActiveSheet = () => sheetInstance.value?.getActiveSheet()
const getData = () => {
  const activeSheet = getActiveSheet()
  return activeSheet ? activeSheet.getData() : []
}
const setData = (data: any[][]) => {
  const activeSheet = getActiveSheet()
  if (activeSheet && activeSheet.tableInstance) {
    activeSheet.tableInstance.setRecords(data)
  }
}
const addRow = (rowData: any[]) => {
  const currentData = getData()
  currentData.push(rowData)
  setData(currentData)
}
const deleteRow = (index: number) => {
  const currentData = getData()
  if (index >= 0 && index < currentData.length) {
    currentData.splice(index, 1)
    setData(currentData)
  }
}
const updateCell = (row: number, col: number, value: any) => {
  const activeSheet = getActiveSheet()
  if (activeSheet) {
    activeSheet.setCellValue(row, col, value)
  }
}

// 暴露方法
defineExpose({
  getSheetInstance,
  getActiveSheet,
  getData,
  setData,
  addRow,
  deleteRow,
  updateCell
})

onMounted(() => {
  initSheet()
})

onUnmounted(() => {
  if (sheetInstance.value) {
    sheetInstance.value.destroy()
    sheetInstance.value = null
  }
})
</script>

<style scoped>
.vtable-sheet-wrapper {
  width: 100%;
  height: 100%;
}

.vtable-sheet-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
}
</style>
